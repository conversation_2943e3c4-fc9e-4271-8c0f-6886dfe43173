/**
 * Next.js Metadata API
 *
 * This file defines the types used by Next.js to configure metadata
 * through static exports or dynamic `generateMetadata` functions in Server Components.
 *
 * @remarks
 * - The static `metadata` object and `generateMetadata` function are only supported in Server Components.
 * - Do not export both a `metadata` object and a `generateMetadata` function from the same route segment.
 * - You can still render metadata in client components directly as part of the component's JSX.
 *
 * @see https://nextjs.org/docs/app/api-reference/metadata
 */ export { };

//# sourceMappingURL=metadata-interface.js.map