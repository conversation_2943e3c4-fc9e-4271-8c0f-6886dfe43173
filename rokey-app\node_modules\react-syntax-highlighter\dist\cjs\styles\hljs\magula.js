"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "backgroundColor": "#f4f4f4",
    "color": "black"
  },
  "hljs-subst": {
    "color": "black"
  },
  "hljs-string": {
    "color": "#050"
  },
  "hljs-title": {
    "color": "navy",
    "fontWeight": "bold"
  },
  "hljs-symbol": {
    "color": "#050"
  },
  "hljs-bullet": {
    "color": "#050"
  },
  "hljs-attribute": {
    "color": "#050"
  },
  "hljs-addition": {
    "color": "#050"
  },
  "hljs-variable": {
    "color": "#050"
  },
  "hljs-template-tag": {
    "color": "#050"
  },
  "hljs-template-variable": {
    "color": "#050"
  },
  "hljs-comment": {
    "color": "#777"
  },
  "hljs-quote": {
    "color": "#777"
  },
  "hljs-number": {
    "color": "#800"
  },
  "hljs-regexp": {
    "color": "#800"
  },
  "hljs-literal": {
    "color": "#800"
  },
  "hljs-type": {
    "color": "#800"
  },
  "hljs-link": {
    "color": "#800"
  },
  "hljs-deletion": {
    "color": "#00e"
  },
  "hljs-meta": {
    "color": "#00e"
  },
  "hljs-keyword": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-selector-tag": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-doctag": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-section": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-built_in": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-tag": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-name": {
    "fontWeight": "bold",
    "color": "navy"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};