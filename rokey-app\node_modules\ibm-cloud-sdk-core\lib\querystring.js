"use strict";
/**
 * (C) Copyright IBM Corp. 2019, 2022.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Lightweight implementation for stringify-ing query params
 *
 * @param queryParams - the query params
 * @returns the query string
 */
var stringify = function (queryParams) {
    return Object.keys(queryParams)
        .map(function (key) { return "".concat(key, "=").concat(encodeURIComponent(queryParams[key])); })
        .join('&');
};
exports.default = {
    stringify: stringify,
};
