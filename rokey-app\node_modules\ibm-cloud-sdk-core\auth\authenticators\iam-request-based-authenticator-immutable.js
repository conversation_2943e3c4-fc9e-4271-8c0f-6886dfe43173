"use strict";
/**
 * (C) Copyright IBM Corp. 2024.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IamRequestBasedAuthenticatorImmutable = void 0;
var iam_request_based_token_manager_1 = require("../token-managers/iam-request-based-token-manager");
var token_request_based_authenticator_immutable_1 = require("./token-request-based-authenticator-immutable");
/**
 * The IamRequestBasedAuthenticatorImmutable provides shared configuration and functionality
 * for authenticators that interact with the IAM token service. This authenticator
 * is not meant for use on its own.
 */
var IamRequestBasedAuthenticatorImmutable = /** @class */ (function (_super) {
    __extends(IamRequestBasedAuthenticatorImmutable, _super);
    /**
     *
     * Create a new IamRequestBasedAuthenticatorImmutable instance.
     *
     * @param options - Configuration options for IAM authentication.
     * This should be an object containing these fields:
     * - url: (optional) the endpoint URL for the token service
     * - disableSslVerification: (optional) a flag that indicates whether verification of the token server's SSL certificate
     * should be disabled or not
     * - headers: (optional) a set of HTTP headers to be sent with each request to the token service
     * - clientId: (optional) the "clientId" and "clientSecret" fields are used to form a Basic
     * Authorization header to be included in each request to the token service
     * - clientSecret: (optional) the "clientId" and "clientSecret" fields are used to form a Basic
     * Authorization header to be included in each request to the token service
     * - scope: (optional) the "scope" parameter to use when fetching the bearer token from the token service
     *
     * @throws Error: the configuration options are not valid.
     */
    function IamRequestBasedAuthenticatorImmutable(options) {
        var _this = this;
        // all parameters are optional
        options = options || {};
        _this = _super.call(this, options) || this;
        _this.clientId = options.clientId;
        _this.clientSecret = options.clientSecret;
        _this.scope = options.scope;
        _this.tokenManager = new iam_request_based_token_manager_1.IamRequestBasedTokenManager(options);
        return _this;
    }
    return IamRequestBasedAuthenticatorImmutable;
}(token_request_based_authenticator_immutable_1.TokenRequestBasedAuthenticatorImmutable));
exports.IamRequestBasedAuthenticatorImmutable = IamRequestBasedAuthenticatorImmutable;
