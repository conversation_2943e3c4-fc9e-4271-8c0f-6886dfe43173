/**
 * Semantic Cache Service for RouKey
 * Provides advanced caching using vector embeddings for semantic similarity
 */

import { jinaEmbeddings } from '@/lib/embeddings/jina';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import crypto from 'crypto';

export interface CacheRequest {
  promptText: string;
  modelUsed: string;
  providerUsed: string;
  temperature?: number;
  maxTokens?: number;
  metadata?: Record<string, any>;
}

export interface CacheResponse {
  responseData: any;
  tokensPrompt?: number;
  tokensCompletion?: number;
  cost?: number;
}

export interface CachedResult {
  id: string;
  promptText: string;
  responseData: any;
  modelUsed: string;
  providerUsed: string;
  similarity: number;
  hitCount: number;
  createdAt: string;
  expiresAt: string;
}

export interface CacheStats {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  tokensSaved: number;
  costSaved: number;
  avgResponseTime: number;
}

export type CacheTier = 'free' | 'starter' | 'pro' | 'enterprise';

export class SemanticCacheService {
  private static instance: SemanticCacheService;
  
  // Cache configuration by tier
  private readonly tierConfig = {
    free: {
      enabled: false,
      ttlHours: 0,
      similarityThreshold: 0.90,
      maxCacheSize: 0
    },
    starter: {
      enabled: false, // Simple caching only for starter
      ttlHours: 0,
      similarityThreshold: 0.90,
      maxCacheSize: 0
    },
    pro: {
      enabled: true,
      ttlHours: 24, // 1 day
      similarityThreshold: 0.85,
      maxCacheSize: 1000
    },
    enterprise: {
      enabled: true,
      ttlHours: 168, // 1 week
      similarityThreshold: 0.80,
      maxCacheSize: 10000
    }
  };

  private constructor() {}

  public static getInstance(): SemanticCacheService {
    if (!SemanticCacheService.instance) {
      SemanticCacheService.instance = new SemanticCacheService();
    }
    return SemanticCacheService.instance;
  }

  /**
   * Generate a hash for exact prompt matching
   */
  private generatePromptHash(prompt: string, model: string, temperature?: number): string {
    const hashInput = `${prompt}|${model}|${temperature || 0}`;
    return crypto.createHash('sha256').update(hashInput).digest('hex');
  }

  /**
   * Check if semantic caching is enabled for the given tier
   */
  private isCacheEnabled(tier: CacheTier): boolean {
    return this.tierConfig[tier].enabled;
  }

  /**
   * Get cache configuration for a tier
   */
  private getTierConfig(tier: CacheTier) {
    return this.tierConfig[tier];
  }

  /**
   * Search for cached responses using semantic similarity
   */
  async searchCache(
    request: CacheRequest,
    userId: string,
    configId: string,
    tier: CacheTier
  ): Promise<CachedResult | null> {
    try {
      if (!this.isCacheEnabled(tier)) {
        return null;
      }

      const config = this.getTierConfig(tier);
      const supabase = createSupabaseServerClient();

      // First, try exact hash match for perfect duplicates
      const promptHash = this.generatePromptHash(
        request.promptText,
        request.modelUsed,
        request.temperature
      );

      const { data: exactMatch, error: exactError } = await supabase
        .from('semantic_cache')
        .select('*')
        .eq('prompt_hash', promptHash)
        .eq('user_id', userId)
        .eq('custom_api_config_id', configId)
        .gt('expires_at', new Date().toISOString())
        .limit(1)
        .single();

      if (exactMatch && !exactError) {
        console.log('[Semantic Cache] Exact hash match found');
        await this.incrementHitCount(exactMatch.id);
        return this.formatCachedResult(exactMatch, 1.0);
      }

      // If no exact match, try semantic similarity search
      const queryEmbedding = await jinaEmbeddings.embedQuery(request.promptText);

      const { data: semanticMatches, error: semanticError } = await supabase
        .rpc('search_semantic_cache', {
          query_embedding: queryEmbedding,
          config_id: configId,
          user_id_param: userId,
          similarity_threshold: config.similarityThreshold,
          match_count: 1
        });

      if (semanticError) {
        console.error('[Semantic Cache] Search error:', semanticError);
        return null;
      }

      if (semanticMatches && semanticMatches.length > 0) {
        const match = semanticMatches[0];
        console.log(`[Semantic Cache] Semantic match found with ${(match.similarity * 100).toFixed(1)}% similarity`);
        await this.incrementHitCount(match.id);
        return this.formatCachedResult(match, match.similarity);
      }

      console.log('[Semantic Cache] No cache match found');
      return null;

    } catch (error) {
      console.error('[Semantic Cache] Search error:', error);
      return null;
    }
  }

  /**
   * Store a response in the semantic cache
   */
  async storeCache(
    request: CacheRequest,
    response: CacheResponse,
    userId: string,
    configId: string,
    tier: CacheTier
  ): Promise<boolean> {
    try {
      if (!this.isCacheEnabled(tier)) {
        return false;
      }

      const config = this.getTierConfig(tier);
      const supabase = createSupabaseServerClient();

      // Generate embedding for the prompt
      const promptEmbedding = await jinaEmbeddings.embedQuery(request.promptText);
      const promptHash = this.generatePromptHash(
        request.promptText,
        request.modelUsed,
        request.temperature
      );

      // Calculate expiration time
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + config.ttlHours);

      // Store in cache
      const { error } = await supabase
        .from('semantic_cache')
        .insert({
          user_id: userId,
          custom_api_config_id: configId,
          prompt_text: request.promptText,
          prompt_embedding: promptEmbedding,
          prompt_hash: promptHash,
          model_used: request.modelUsed,
          provider_used: request.providerUsed,
          temperature: request.temperature,
          max_tokens: request.maxTokens,
          request_metadata: request.metadata || {},
          response_data: response.responseData,
          response_tokens_prompt: response.tokensPrompt,
          response_tokens_completion: response.tokensCompletion,
          response_cost: response.cost,
          cache_tier: tier,
          expires_at: expiresAt.toISOString()
        });

      if (error) {
        console.error('[Semantic Cache] Store error:', error);
        return false;
      }

      console.log(`[Semantic Cache] Stored response for ${tier} tier (expires: ${expiresAt.toISOString()})`);
      return true;

    } catch (error) {
      console.error('[Semantic Cache] Store error:', error);
      return false;
    }
  }

  /**
   * Get cache statistics for a user/config
   */
  async getCacheStats(
    userId: string,
    configId: string,
    days: number = 7
  ): Promise<CacheStats> {
    try {
      const supabase = createSupabaseServerClient();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('semantic_cache_analytics')
        .select('*')
        .eq('user_id', userId)
        .eq('custom_api_config_id', configId)
        .gte('date', startDate.toISOString().split('T')[0]);

      if (error) {
        console.error('[Semantic Cache] Stats error:', error);
        return this.getEmptyStats();
      }

      if (!data || data.length === 0) {
        return this.getEmptyStats();
      }

      // Aggregate statistics
      const totals = data.reduce((acc, row) => ({
        totalRequests: acc.totalRequests + (row.total_requests || 0),
        cacheHits: acc.cacheHits + (row.cache_hits || 0),
        cacheMisses: acc.cacheMisses + (row.cache_misses || 0),
        tokensSaved: acc.tokensSaved + (row.tokens_saved || 0),
        costSaved: acc.costSaved + (row.cost_saved || 0),
        responseTimeSum: acc.responseTimeSum + (row.avg_response_time_ms || 0),
        rowCount: acc.rowCount + 1
      }), {
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        tokensSaved: 0,
        costSaved: 0,
        responseTimeSum: 0,
        rowCount: 0
      });

      return {
        totalRequests: totals.totalRequests,
        cacheHits: totals.cacheHits,
        cacheMisses: totals.cacheMisses,
        hitRate: totals.totalRequests > 0 ? totals.cacheHits / totals.totalRequests : 0,
        tokensSaved: totals.tokensSaved,
        costSaved: totals.costSaved,
        avgResponseTime: totals.rowCount > 0 ? totals.responseTimeSum / totals.rowCount : 0
      };

    } catch (error) {
      console.error('[Semantic Cache] Stats error:', error);
      return this.getEmptyStats();
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpiredCache(): Promise<number> {
    try {
      const supabase = createSupabaseServerClient();
      
      const { data, error } = await supabase
        .rpc('cleanup_semantic_cache');

      if (error) {
        console.error('[Semantic Cache] Cleanup error:', error);
        return 0;
      }

      console.log(`[Semantic Cache] Cleaned up ${data} expired entries`);
      return data || 0;

    } catch (error) {
      console.error('[Semantic Cache] Cleanup error:', error);
      return 0;
    }
  }

  /**
   * Private helper methods
   */
  private async incrementHitCount(cacheId: string): Promise<void> {
    try {
      const supabase = createSupabaseServerClient();
      await supabase.rpc('increment_cache_hit', { cache_id: cacheId });
    } catch (error) {
      console.error('[Semantic Cache] Hit count increment error:', error);
    }
  }

  private formatCachedResult(cacheEntry: any, similarity: number): CachedResult {
    return {
      id: cacheEntry.id,
      promptText: cacheEntry.prompt_text,
      responseData: cacheEntry.response_data,
      modelUsed: cacheEntry.model_used,
      providerUsed: cacheEntry.provider_used,
      similarity,
      hitCount: cacheEntry.hit_count,
      createdAt: cacheEntry.created_at,
      expiresAt: cacheEntry.expires_at
    };
  }

  private getEmptyStats(): CacheStats {
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      tokensSaved: 0,
      costSaved: 0,
      avgResponseTime: 0
    };
  }
}

// Export singleton instance
export const semanticCache = SemanticCacheService.getInstance();
